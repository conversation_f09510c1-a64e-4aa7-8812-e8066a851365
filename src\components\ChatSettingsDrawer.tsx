import React, { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useAppStore } from '../store'
import { X, MessageSquare, Zap, Save } from './Icons'
import ModelSelector from './ModelSelector'
import { modelPresets } from '../utils/modelUtils'

interface ChatSettingsDrawerProps {
  isOpen: boolean
  onClose: () => void
}

const ChatSettingsDrawer: React.FC<ChatSettingsDrawerProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings } = useAppStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [activeTab, setActiveTab] = useState<'model' | 'advanced' | 'prompts'>('model')

  useEffect(() => {
    setLocalSettings(settings)
  }, [settings])

  const handleSave = async () => {
    try {
      // Update settings in store and save to electron
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }
      
      onClose()
    } catch (error) {
      console.error('Failed to save chat settings:', error)
    }
  }

  const applyPreset = (preset: typeof modelPresets[0]) => {
    setLocalSettings({
      ...localSettings,
      temperature: preset.temperature,
      topP: preset.topP,
      topK: preset.topK,
      maxTokens: preset.maxTokens || localSettings.maxTokens
    })
  }

  if (!isOpen) return null

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
      onClick={onClose}
    >
      <div
        className="bg-gray-800 border border-gray-700 rounded-lg max-w-7xl w-full max-h-[95vh] overflow-y-auto shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-supplement1">Chat Settings</h2>
            <p className="text-sm text-gray-400 mt-1">Configure your AI model and conversation preferences</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
            title="Close settings"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Tabs */}
          <div className="flex space-x-6 border-b border-gray-700 mb-6">
            <button
              className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'model'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('model')}
            >
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Model & Presets
              </div>
            </button>
            <button
              className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'advanced'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('advanced')}
            >
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Advanced
              </div>
            </button>
            <button
              className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'prompts'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('prompts')}
            >
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                System Prompt
              </div>
            </button>

          {/* Tab Content */}
          <div className="space-y-8">
            {/* Model & Presets Tab */}
            {activeTab === 'model' && (
              <div className="space-y-8">
                {/* Model Selection */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">
                    AI Model
                  </label>
                  <ModelSelector
                    selectedModel={localSettings.selectedModel}
                    onModelSelect={(modelId) => setLocalSettings({ ...localSettings, selectedModel: modelId })}
                  />
                </div>

                {/* Configuration Presets */}
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium mb-3 text-supplement1">
                    <Zap className="h-4 w-4" />
                    Quick Presets
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    {modelPresets.map((preset) => (
                      <button
                        key={preset.name}
                        type="button"
                        onClick={() => applyPreset(preset)}
                        className="p-4 text-left border border-gray-600 rounded-lg hover:border-primary hover:bg-gray-700/50 transition-all group"
                      >
                        <div className="font-medium text-sm mb-1 text-supplement1 group-hover:text-primary">{preset.name}</div>
                        <div className="text-xs text-gray-400 mb-2">{preset.description}</div>
                        <div className="text-xs text-gray-500">
                          T:{preset.temperature} • P:{preset.topP} • K:{preset.topK}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Tab */}
            {activeTab === 'advanced' && (
              <div className="space-y-8">
                {/* Temperature */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">
                    Temperature: {localSettings.temperature?.toFixed(1) || '0.7'}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={localSettings.temperature || 0.7}
                    onChange={(e) => setLocalSettings({ ...localSettings, temperature: parseFloat(e.target.value) })}
                    className="w-full accent-primary"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Deterministic</span>
                    <span>Balanced</span>
                    <span>Creative</span>
                  </div>
                </div>

                {/* Top-P */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">
                    Top-P (Nucleus): {localSettings.topP?.toFixed(2) || '0.95'}
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1.0"
                    step="0.05"
                    value={localSettings.topP || 0.95}
                    onChange={(e) => setLocalSettings({ ...localSettings, topP: parseFloat(e.target.value) })}
                    className="w-full accent-primary"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Focused</span>
                    <span>Balanced</span>
                    <span>Diverse</span>
                  </div>
                </div>

                {/* Top-K */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">
                    Top-K: {localSettings.topK || 30}
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="100"
                    step="1"
                    value={localSettings.topK || 30}
                    onChange={(e) => setLocalSettings({ ...localSettings, topK: parseInt(e.target.value) })}
                    className="w-full accent-primary"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Conservative</span>
                    <span>Balanced</span>
                    <span>Diverse</span>
                  </div>
                </div>

                {/* Max Tokens */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">
                    Max Tokens: {localSettings.maxTokens?.toLocaleString() || '4,096'}
                  </label>
                  <input
                    type="range"
                    min="256"
                    max="100000"
                    step="256"
                    value={localSettings.maxTokens || 4096}
                    onChange={(e) => setLocalSettings({ ...localSettings, maxTokens: parseInt(e.target.value) })}
                    className="w-full accent-primary"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>256</span>
                    <span>4K</span>
                    <span>100K+</span>
                  </div>
                  <p className="text-xs text-gray-400 mt-2">
                    Higher values allow longer responses but cost more. Some models support up to 100K+ tokens.
                  </p>
                </div>
              </div>
            )}

            {/* System Prompt Tab */}
            {activeTab === 'prompts' && (
              <div className="space-y-8">
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium mb-3 text-supplement1">
                    <MessageSquare className="h-4 w-4" />
                    System Prompt
                  </label>
                  <textarea
                    value={localSettings.systemPrompt || ''}
                    onChange={(e) => setLocalSettings({ ...localSettings, systemPrompt: e.target.value })}
                    placeholder="Enter a system prompt to set the AI's behavior and context..."
                    className="w-full h-40 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-sm placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent outline-none resize-none"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    System prompts help define the AI's role and behavior for all conversations.
                    This will be added to the beginning of every conversation.
                  </p>
                </div>

                {/* System Prompt Templates */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-supplement1">Quick Templates</label>
                  <div className="space-y-3">
                    {[
                      { name: 'Helpful Assistant', prompt: 'You are a helpful, harmless, and honest AI assistant. Provide clear, accurate, and concise responses.' },
                      { name: 'Code Assistant', prompt: 'You are an expert programmer and code reviewer. Help with coding questions, debugging, and best practices. Always explain your reasoning.' },
                      { name: 'Writing Helper', prompt: 'You are a skilled writer and editor. Help improve writing clarity, style, and grammar while maintaining the author\'s voice.' },
                      { name: 'Research Assistant', prompt: 'You are a thorough research assistant. Provide well-sourced, factual information and cite your reasoning when possible.' }
                    ].map((template) => (
                      <button
                        key={template.name}
                        onClick={() => setLocalSettings({ ...localSettings, systemPrompt: template.prompt })}
                        className="w-full text-left p-3 border border-gray-600 rounded-lg hover:border-primary hover:bg-gray-700/50 transition-all group"
                      >
                        <div className="font-medium text-sm text-supplement1 group-hover:text-primary">{template.name}</div>
                        <div className="text-xs text-gray-400 mt-1 line-clamp-2">{template.prompt}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700">
          <div className="text-sm text-gray-400">
            Changes will be applied to new conversations
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-400 hover:text-supplement1 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary text-gray-900 rounded-md hover:bg-primary/90 transition-colors flex items-center gap-2 font-medium"
            >
              <Save className="h-4 w-4" />
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  // Use portal to render modal outside of sidebar hierarchy
  return createPortal(modalContent, document.body)
}

export default ChatSettingsDrawer
