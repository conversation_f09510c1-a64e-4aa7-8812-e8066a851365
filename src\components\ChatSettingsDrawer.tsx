import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { X, Slide<PERSON>, Brain, MessageSquare, Zap, Save } from './Icons'
import ModelSelector from './ModelSelector'
import { modelPresets } from '../utils/modelUtils'

interface ChatSettingsDrawerProps {
  isOpen: boolean
  onClose: () => void
}

const ChatSettingsDrawer: React.FC<ChatSettingsDrawerProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings } = useAppStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [activeTab, setActiveTab] = useState<'model' | 'advanced' | 'prompts'>('model')

  useEffect(() => {
    setLocalSettings(settings)
  }, [settings])

  const handleSave = async () => {
    try {
      // Update settings in store and save to electron
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }
      
      onClose()
    } catch (error) {
      console.error('Failed to save chat settings:', error)
    }
  }

  const applyPreset = (preset: typeof modelPresets[0]) => {
    setLocalSettings({
      ...localSettings,
      temperature: preset.temperature,
      topP: preset.topP,
      topK: preset.topK,
      maxTokens: preset.maxTokens || localSettings.maxTokens
    })
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className={`
        fixed bottom-0 left-0 right-0 bg-neutral-900 border-t border-neutral-700 z-50
        transform transition-transform duration-300 ease-out
        ${isOpen ? 'translate-y-0' : 'translate-y-full'}
        max-h-[80vh] overflow-hidden flex flex-col
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-700">
          <div className="flex items-center gap-2">
            <Sliders className="h-5 w-5 text-indigo-400" />
            <h2 className="text-lg font-semibold">Chat Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-800 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-neutral-700">
          {[
            { id: 'model' as const, label: 'Model & Presets', icon: Brain },
            { id: 'advanced' as const, label: 'Advanced', icon: Sliders },
            { id: 'prompts' as const, label: 'System Prompt', icon: MessageSquare }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`
                flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors
                ${activeTab === id 
                  ? 'text-indigo-400 border-b-2 border-indigo-400' 
                  : 'text-neutral-400 hover:text-neutral-300'
                }
              `}
            >
              <Icon className="h-4 w-4" />
              {label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Model & Presets Tab */}
          {activeTab === 'model' && (
            <div className="space-y-6">
              {/* Model Selection */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  AI Model
                </label>
                <ModelSelector
                  selectedModel={localSettings.selectedModel}
                  onModelSelect={(modelId) => setLocalSettings({ ...localSettings, selectedModel: modelId })}
                />
              </div>

              {/* Configuration Presets */}
              <div>
                <label className="flex items-center gap-2 text-sm font-medium mb-3">
                  <Zap className="h-4 w-4" />
                  Quick Presets
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {modelPresets.map((preset) => (
                    <button
                      key={preset.name}
                      type="button"
                      onClick={() => applyPreset(preset)}
                      className="p-3 text-left border border-neutral-700 rounded-lg hover:border-neutral-600 hover:bg-neutral-800/50 transition-all"
                    >
                      <div className="font-medium text-sm mb-1">{preset.name}</div>
                      <div className="text-xs text-neutral-400 mb-2">{preset.description}</div>
                      <div className="text-xs text-neutral-500">
                        T:{preset.temperature} • P:{preset.topP} • K:{preset.topK}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              {/* Temperature */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Temperature: {localSettings.temperature?.toFixed(1) || '0.7'}
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={localSettings.temperature || 0.7}
                  onChange={(e) => setLocalSettings({ ...localSettings, temperature: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-neutral-500 mt-1">
                  <span>Deterministic</span>
                  <span>Balanced</span>
                  <span>Creative</span>
                </div>
              </div>

              {/* Top-P */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Top-P (Nucleus): {localSettings.topP?.toFixed(2) || '0.95'}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.05"
                  value={localSettings.topP || 0.95}
                  onChange={(e) => setLocalSettings({ ...localSettings, topP: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-neutral-500 mt-1">
                  <span>Focused</span>
                  <span>Balanced</span>
                  <span>Diverse</span>
                </div>
              </div>

              {/* Top-K */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Top-K: {localSettings.topK || 30}
                </label>
                <input
                  type="range"
                  min="1"
                  max="100"
                  step="1"
                  value={localSettings.topK || 30}
                  onChange={(e) => setLocalSettings({ ...localSettings, topK: parseInt(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-neutral-500 mt-1">
                  <span>Conservative</span>
                  <span>Balanced</span>
                  <span>Diverse</span>
                </div>
              </div>

              {/* Max Tokens */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Max Tokens: {localSettings.maxTokens?.toLocaleString() || '4,096'}
                </label>
                <input
                  type="range"
                  min="256"
                  max="100000"
                  step="256"
                  value={localSettings.maxTokens || 4096}
                  onChange={(e) => setLocalSettings({ ...localSettings, maxTokens: parseInt(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-neutral-500 mt-1">
                  <span>256</span>
                  <span>4K</span>
                  <span>100K+</span>
                </div>
                <p className="text-xs text-neutral-500 mt-1">
                  Higher values allow longer responses but cost more. Some models support up to 100K+ tokens.
                </p>
              </div>
            </div>
          )}

          {/* System Prompt Tab */}
          {activeTab === 'prompts' && (
            <div className="space-y-6">
              <div>
                <label className="flex items-center gap-2 text-sm font-medium mb-2">
                  <MessageSquare className="h-4 w-4" />
                  System Prompt
                </label>
                <textarea
                  value={localSettings.systemPrompt || ''}
                  onChange={(e) => setLocalSettings({ ...localSettings, systemPrompt: e.target.value })}
                  placeholder="Enter a system prompt to set the AI's behavior and context..."
                  className="w-full h-32 bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-3 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none resize-none"
                />
                <p className="text-xs text-neutral-500 mt-2">
                  System prompts help define the AI's role and behavior for all conversations. 
                  This will be added to the beginning of every conversation.
                </p>
              </div>

              {/* System Prompt Templates */}
              <div>
                <label className="block text-sm font-medium mb-2">Quick Templates</label>
                <div className="space-y-2">
                  {[
                    { name: 'Helpful Assistant', prompt: 'You are a helpful, harmless, and honest AI assistant. Provide clear, accurate, and concise responses.' },
                    { name: 'Code Assistant', prompt: 'You are an expert programmer and code reviewer. Help with coding questions, debugging, and best practices. Always explain your reasoning.' },
                    { name: 'Writing Helper', prompt: 'You are a skilled writer and editor. Help improve writing clarity, style, and grammar while maintaining the author\'s voice.' },
                    { name: 'Research Assistant', prompt: 'You are a thorough research assistant. Provide well-sourced, factual information and cite your reasoning when possible.' }
                  ].map((template) => (
                    <button
                      key={template.name}
                      onClick={() => setLocalSettings({ ...localSettings, systemPrompt: template.prompt })}
                      className="w-full text-left p-3 border border-neutral-700 rounded-lg hover:border-neutral-600 hover:bg-neutral-800/50 transition-all"
                    >
                      <div className="font-medium text-sm">{template.name}</div>
                      <div className="text-xs text-neutral-400 mt-1 line-clamp-2">{template.prompt}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-4 border-t border-neutral-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="btn-primary flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            Save Settings
          </button>
        </div>
      </div>
    </>
  )
}

export default ChatSettingsDrawer
